#!/usr/bin/env python3
"""
Search for <PERSON>'s account on Truth Social to find the correct handle.
"""

import json
from truthbrush.api import Api

def main():
    api = Api()
    
    # Search for Trump-related accounts
    search_terms = ["<PERSON>", "realDonald<PERSON>rump", "<PERSON>"]
    
    for term in search_terms:
        print(f"\nSearching for: '{term}'")
        print("-" * 40)
        
        try:
            # Search for accounts
            for result_batch in api.search(
                searchtype="accounts",  # Search for user accounts
                query=term,
                limit=5  # Limit results
            ):
                accounts = result_batch.get('accounts', [])
                
                for account in accounts:
                    print(f"Handle: @{account.get('acct', 'N/A')}")
                    print(f"Display Name: {account.get('display_name', 'N/A')}")
                    print(f"Followers: {account.get('followers_count', 'N/A')}")
                    print(f"Verified: {account.get('verified', False)}")
                    print(f"Bio: {account.get('note', 'N/A')[:100]}...")
                    print("-" * 20)
                
                break  # Just get first batch
                
        except Exception as e:
            print(f"Error searching for '{term}': {e}")

if __name__ == "__main__":
    main()
