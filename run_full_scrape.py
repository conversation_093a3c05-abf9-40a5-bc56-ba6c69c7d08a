#!/usr/bin/env python3
"""
Complete pipeline to scrape 2 years of <PERSON>'s Truth Social posts for NLP stance detection.
"""

import os
import sys
from datetime import datetime, timezone, timedelta
from historical_scraper import HistoricalScraper
from prepare_nlp_data import NLPDataPreparator

def check_requirements():
    """Check if all requirements are met."""
    print("🔍 Checking requirements...")
    
    # Check if credentials are set
    if not os.getenv('TRUTHSOCIAL_USERNAME') or not os.getenv('TRUTHSOCIAL_PASSWORD'):
        print("❌ Truth Social credentials not found!")
        print("   Please set TRUTHSOCIAL_USERNAME and TRUTHSOCIAL_PASSWORD in your .env file")
        return False
        
    # Check if pandas is available for data processing
    try:
        import pandas as pd
        print("✅ Pandas available for data processing")
    except ImportError:
        print("❌ Pandas not found! Installing...")
        os.system("pip install pandas")
        
    print("✅ All requirements met")
    return True

def estimate_scraping_time(days_back=730):
    """Estimate how long the scraping will take."""
    # Rough estimates based on API rate limits and post frequency
    estimated_posts_per_day = 10  # Trump posts frequently
    total_estimated_posts = days_back * estimated_posts_per_day
    
    # With rate limiting (0.5s per post) + API calls
    estimated_seconds = total_estimated_posts * 0.7  # Conservative estimate
    estimated_minutes = estimated_seconds / 60
    estimated_hours = estimated_minutes / 60
    
    print(f"⏱️  Scraping Estimates:")
    print(f"   Days to scrape: {days_back}")
    print(f"   Estimated posts: ~{total_estimated_posts:,}")
    print(f"   Estimated time: ~{estimated_hours:.1f} hours ({estimated_minutes:.0f} minutes)")
    print(f"   Note: This is a conservative estimate. Actual time may vary.")
    
    return estimated_hours

def run_complete_pipeline():
    """Run the complete scraping and preparation pipeline."""
    
    print("🚀 TRUMP TRUTH SOCIAL NLP DATASET CREATION")
    print("="*60)
    print("Purpose: Collect 2 years of posts for stance detection analysis")
    print("Target: @realDonaldTrump on Truth Social")
    print("="*60)
    
    # Check requirements
    if not check_requirements():
        return False
    
    # Estimate time
    days_back = 730  # 2 years
    estimated_hours = estimate_scraping_time(days_back)
    
    # Ask for confirmation
    print(f"\n⚠️  This will scrape approximately 2 years of data.")
    print(f"   Estimated time: ~{estimated_hours:.1f} hours")
    print(f"   The script will run continuously and save progress in batches.")
    
    response = input("\nDo you want to continue? (y/N): ").strip().lower()
    if response != 'y':
        print("❌ Scraping cancelled by user")
        return False
    
    # Phase 1: Historical Scraping
    print(f"\n📥 PHASE 1: HISTORICAL DATA COLLECTION")
    print("-" * 40)
    
    scraper = HistoricalScraper()
    
    # Define date range
    end_date = datetime.now(timezone.utc)
    start_date = end_date - timedelta(days=days_back)
    
    print(f"📅 Scraping from {start_date.strftime('%Y-%m-%d')} to {end_date.strftime('%Y-%m-%d')}")
    
    try:
        total_posts = scraper.scrape_historical_posts(start_date, end_date, batch_size=50)
        
        if total_posts == 0:
            print("❌ No posts collected. Check your credentials and try again.")
            return False
            
        print(f"✅ Phase 1 complete: {total_posts} posts collected")
        
    except Exception as e:
        print(f"❌ Error during scraping: {e}")
        return False
    
    # Phase 2: Data Preparation
    print(f"\n🔧 PHASE 2: NLP DATA PREPARATION")
    print("-" * 40)
    
    try:
        # Find the dataset file
        import glob
        data_files = glob.glob("historical_data/trump_historical_dataset_*.json")
        if not data_files:
            print("❌ No dataset file found!")
            return False
            
        latest_file = max(data_files)
        print(f"📂 Processing: {latest_file}")
        
        # Prepare data for NLP
        preparator = NLPDataPreparator(latest_file)
        preparator.load_data()
        preparator.clean_text_for_nlp()
        preparator.add_temporal_features()
        
        # Identify topics
        topics = preparator.identify_potential_topics(top_n=20)
        preparator.create_topic_features(topics)
        preparator.add_engagement_features()
        
        # Save processed data
        csv_file, json_file = preparator.save_processed_data()
        
        # Generate report
        preparator.generate_data_report()
        
        print(f"✅ Phase 2 complete: NLP-ready data saved")
        
    except Exception as e:
        print(f"❌ Error during data preparation: {e}")
        return False
    
    # Final Summary
    print(f"\n🎉 PIPELINE COMPLETE!")
    print("="*60)
    print(f"📊 Dataset ready for NLP stance detection analysis")
    print(f"📁 Files created:")
    print(f"   Raw data: historical_data/")
    print(f"   NLP data: nlp_ready_data/")
    print(f"   CSV file: {csv_file}")
    print(f"   JSON file: {json_file}")
    
    print(f"\n🔬 Next Steps for Stance Detection:")
    print(f"   1. Load the CSV file into your NLP framework")
    print(f"   2. Use 'cleaned_content' as input text")
    print(f"   3. Use topic features and engagement metrics as additional features")
    print(f"   4. Consider temporal features for time-series analysis")
    print(f"   5. Split data chronologically for training/testing")
    
    return True

def quick_test_scrape():
    """Run a quick test scrape to verify everything works."""
    print("🧪 QUICK TEST SCRAPE")
    print("-" * 30)
    print("Testing with last 7 days of data...")
    
    scraper = HistoricalScraper()
    end_date = datetime.now(timezone.utc)
    start_date = end_date - timedelta(days=7)
    
    try:
        total_posts = scraper.scrape_historical_posts(start_date, end_date, batch_size=10)
        print(f"✅ Test successful: {total_posts} posts collected")
        return True
    except Exception as e:
        print(f"❌ Test failed: {e}")
        return False

def main():
    """Main function with options."""
    if len(sys.argv) > 1 and sys.argv[1] == "--test":
        # Run quick test
        success = quick_test_scrape()
    else:
        # Run full pipeline
        success = run_complete_pipeline()
    
    if success:
        print(f"\n✨ Success! Your NLP dataset is ready.")
    else:
        print(f"\n💥 Something went wrong. Check the error messages above.")
    
    return success

if __name__ == "__main__":
    main()
