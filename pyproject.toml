[tool.poetry]
name = "truthbrush"
version = "0.1.9"
description = "API client for Truth Social"
authors = ["<PERSON><PERSON> <<EMAIL>>"]
license = "Apache 2.0"
readme = "README.md"

[tool.poetry.scripts]
truthbrush = "truthbrush.cli:cli"

[tool.poetry.dependencies]
python = "^3.10"
click = "^8.1.0"
loguru = "^0.7.0"
python-dotenv = "^1.0.1"
python-dateutil = "2.9.0"
curl_cffi = "^0.7.0"

[tool.poetry.dev-dependencies]
black = "^24.3.0"
pytest = "^7.0.1"
pylint = "^2.12.2"

[build-system]
requires = ["poetry-core>=1.0.0"]
build-backend = "poetry.core.masonry.api"
