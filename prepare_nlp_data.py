#!/usr/bin/env python3
"""
Prepare scraped Truth Social data for NLP stance detection analysis.
"""

import json
import pandas as pd
import re
from datetime import datetime
from collections import Counter
import os

class NLPDataPreparator:
    def __init__(self, data_file):
        self.data_file = data_file
        self.df = None
        
    def load_data(self):
        """Load the scraped data into a pandas DataFrame."""
        print(f"Loading data from {self.data_file}...")
        
        with open(self.data_file, 'r', encoding='utf-8') as f:
            data = json.load(f)
            
        # Flatten the nested structure for easier analysis
        flattened_data = []
        for post in data:
            flat_post = {
                'id': post['id'],
                'created_at': post['created_at'],
                'content': post['content'],
                'content_length': post['content_length'],
                'word_count': post['nlp_features']['word_count'],
                'sentence_count': post['nlp_features']['sentence_count'],
                'exclamation_count': post['nlp_features']['exclamation_count'],
                'question_count': post['nlp_features']['question_count'],
                'caps_ratio': post['nlp_features']['caps_ratio'],
                'mention_count': post['nlp_features']['mention_count'],
                'hashtag_count': post['nlp_features']['hashtag_count'],
                'likes': post['engagement']['likes'],
                'reblogs': post['engagement']['reblogs'],
                'replies': post['engagement']['replies'],
                'has_media': post['has_media'],
                'mentions': ','.join(post['mentions']),
                'hashtags': ','.join(post['hashtags']),
                'url': post['metadata']['url']
            }
            flattened_data.append(flat_post)
            
        self.df = pd.DataFrame(flattened_data)
        self.df['created_at'] = pd.to_datetime(self.df['created_at'])
        
        print(f"✅ Loaded {len(self.df)} posts")
        return self.df
    
    def clean_text_for_nlp(self):
        """Clean and preprocess text for NLP analysis."""
        print("🧹 Cleaning text for NLP...")
        
        def clean_text(text):
            if pd.isna(text) or text == '':
                return ''
                
            # Remove URLs
            text = re.sub(r'http[s]?://(?:[a-zA-Z]|[0-9]|[$-_@.&+]|[!*\\(\\),]|(?:%[0-9a-fA-F][0-9a-fA-F]))+', '', text)
            
            # Remove extra whitespace
            text = re.sub(r'\s+', ' ', text)
            
            # Remove leading/trailing whitespace
            text = text.strip()
            
            return text
        
        self.df['cleaned_content'] = self.df['content'].apply(clean_text)
        
        # Filter out very short posts (likely not useful for stance detection)
        original_count = len(self.df)
        self.df = self.df[self.df['word_count'] >= 3]  # At least 3 words
        filtered_count = len(self.df)
        
        print(f"   Filtered out {original_count - filtered_count} posts with <3 words")
        print(f"   Remaining posts: {filtered_count}")
        
    def add_temporal_features(self):
        """Add temporal features that might be relevant for stance detection."""
        print("📅 Adding temporal features...")
        
        self.df['year'] = self.df['created_at'].dt.year
        self.df['month'] = self.df['created_at'].dt.month
        self.df['day_of_week'] = self.df['created_at'].dt.dayofweek
        self.df['hour'] = self.df['created_at'].dt.hour
        
        # Add relative time features
        min_date = self.df['created_at'].min()
        self.df['days_since_start'] = (self.df['created_at'] - min_date).dt.days
        
    def identify_potential_topics(self, top_n=20):
        """Identify potential topics/themes for stance detection."""
        print(f"🔍 Identifying top {top_n} topics...")
        
        # Combine all text
        all_text = ' '.join(self.df['cleaned_content'].fillna(''))
        
        # Extract keywords (simple approach)
        words = re.findall(r'\b[a-zA-Z]{3,}\b', all_text.lower())
        
        # Filter out common stop words
        stop_words = {
            'the', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by',
            'a', 'an', 'is', 'are', 'was', 'were', 'be', 'been', 'have', 'has', 'had',
            'do', 'does', 'did', 'will', 'would', 'could', 'should', 'may', 'might',
            'must', 'can', 'this', 'that', 'these', 'those', 'i', 'you', 'he', 'she',
            'it', 'we', 'they', 'me', 'him', 'her', 'us', 'them', 'my', 'your', 'his',
            'her', 'its', 'our', 'their', 'all', 'any', 'both', 'each', 'few', 'more',
            'most', 'other', 'some', 'such', 'no', 'nor', 'not', 'only', 'own', 'same',
            'so', 'than', 'too', 'very', 'just', 'now', 'get', 'got', 'make', 'made',
            'take', 'took', 'come', 'came', 'go', 'went', 'see', 'saw', 'know', 'knew'
        }
        
        filtered_words = [word for word in words if word not in stop_words and len(word) > 3]
        word_counts = Counter(filtered_words)
        
        top_topics = word_counts.most_common(top_n)
        
        print("   Top topics found:")
        for topic, count in top_topics:
            print(f"     {topic}: {count} mentions")
            
        return top_topics
    
    def create_topic_features(self, topics):
        """Create binary features for topic presence."""
        print("🏷️  Creating topic features...")
        
        for topic, _ in topics:
            # Create binary feature for each topic
            self.df[f'topic_{topic}'] = self.df['cleaned_content'].str.lower().str.contains(topic, na=False)
            
    def add_engagement_features(self):
        """Add engagement-based features."""
        print("📈 Adding engagement features...")
        
        # Engagement ratios
        self.df['engagement_total'] = self.df['likes'] + self.df['reblogs'] + self.df['replies']
        self.df['like_ratio'] = self.df['likes'] / (self.df['engagement_total'] + 1)
        self.df['reblog_ratio'] = self.df['reblogs'] / (self.df['engagement_total'] + 1)
        self.df['reply_ratio'] = self.df['replies'] / (self.df['engagement_total'] + 1)
        
        # Engagement per word
        self.df['engagement_per_word'] = self.df['engagement_total'] / (self.df['word_count'] + 1)
        
    def save_processed_data(self, output_dir="nlp_ready_data"):
        """Save the processed data in multiple formats."""
        if not os.path.exists(output_dir):
            os.makedirs(output_dir)
            
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        
        # Save as CSV for easy analysis
        csv_file = os.path.join(output_dir, f"trump_nlp_dataset_{timestamp}.csv")
        self.df.to_csv(csv_file, index=False, encoding='utf-8')
        print(f"💾 Saved CSV: {csv_file}")
        
        # Save as JSON for programmatic use
        json_file = os.path.join(output_dir, f"trump_nlp_dataset_{timestamp}.json")
        self.df.to_json(json_file, orient='records', date_format='iso', indent=2)
        print(f"💾 Saved JSON: {json_file}")
        
        # Save feature summary
        feature_summary = {
            'total_posts': len(self.df),
            'date_range': {
                'start': self.df['created_at'].min().isoformat(),
                'end': self.df['created_at'].max().isoformat()
            },
            'features': list(self.df.columns),
            'text_stats': {
                'avg_word_count': self.df['word_count'].mean(),
                'avg_engagement': self.df['engagement_total'].mean(),
                'posts_with_media': self.df['has_media'].sum()
            }
        }
        
        summary_file = os.path.join(output_dir, f"feature_summary_{timestamp}.json")
        with open(summary_file, 'w', encoding='utf-8') as f:
            json.dump(feature_summary, f, indent=2, default=str)
        print(f"📋 Saved summary: {summary_file}")
        
        return csv_file, json_file
    
    def generate_data_report(self):
        """Generate a comprehensive data report."""
        print("\n" + "="*60)
        print("📊 NLP DATASET REPORT")
        print("="*60)
        
        print(f"📈 Dataset Size: {len(self.df):,} posts")
        print(f"📅 Date Range: {self.df['created_at'].min().strftime('%Y-%m-%d')} to {self.df['created_at'].max().strftime('%Y-%m-%d')}")
        print(f"📝 Total Words: {self.df['word_count'].sum():,}")
        print(f"💬 Average Words per Post: {self.df['word_count'].mean():.1f}")
        print(f"❤️  Total Engagement: {self.df['engagement_total'].sum():,}")
        print(f"📸 Posts with Media: {self.df['has_media'].sum():,} ({self.df['has_media'].mean()*100:.1f}%)")
        
        print(f"\n📊 Engagement Statistics:")
        print(f"   Likes: {self.df['likes'].sum():,} (avg: {self.df['likes'].mean():.0f})")
        print(f"   Reblogs: {self.df['reblogs'].sum():,} (avg: {self.df['reblogs'].mean():.0f})")
        print(f"   Replies: {self.df['replies'].sum():,} (avg: {self.df['replies'].mean():.0f})")
        
        print(f"\n📅 Temporal Distribution:")
        yearly_counts = self.df['year'].value_counts().sort_index()
        for year, count in yearly_counts.items():
            print(f"   {year}: {count:,} posts")

def main():
    """Main function to prepare NLP data."""
    import glob
    
    # Find the most recent historical dataset
    data_files = glob.glob("historical_data/trump_historical_dataset_*.json")
    if not data_files:
        print("❌ No historical dataset found!")
        print("   Please run historical_scraper.py first")
        return
        
    latest_file = max(data_files)
    print(f"📂 Using dataset: {latest_file}")
    
    # Initialize preparator
    preparator = NLPDataPreparator(latest_file)
    
    # Process the data
    preparator.load_data()
    preparator.clean_text_for_nlp()
    preparator.add_temporal_features()
    
    # Identify topics
    topics = preparator.identify_potential_topics(top_n=15)
    preparator.create_topic_features(topics)
    
    # Add engagement features
    preparator.add_engagement_features()
    
    # Save processed data
    csv_file, json_file = preparator.save_processed_data()
    
    # Generate report
    preparator.generate_data_report()
    
    print(f"\n🎉 Data preparation complete!")
    print(f"📁 NLP-ready files saved in: nlp_ready_data/")
    print(f"🔬 Ready for stance detection analysis!")

if __name__ == "__main__":
    main()
