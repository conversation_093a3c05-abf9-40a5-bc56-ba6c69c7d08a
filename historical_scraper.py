#!/usr/bin/env python3
"""
Historical Truth Social scraper for NLP stance detection.
Collects posts from 2 years ago until now with proper data structure for NLP analysis.
"""

import json
import os
import time
from datetime import datetime, timezone, timedelta
from truthbrush.api import Api
import logging

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class HistoricalScraper:
    def __init__(self, username="realDonaldTrump"):
        self.api = Api()
        self.username = username
        self.output_dir = "historical_data"
        self.ensure_output_dir()
        
    def ensure_output_dir(self):
        """Create output directory if it doesn't exist."""
        if not os.path.exists(self.output_dir):
            os.makedirs(self.output_dir)
            
    def scrape_historical_posts(self, start_date=None, end_date=None, batch_size=100):
        """
        Scrape posts from start_date to end_date.
        
        Args:
            start_date: datetime object (default: 2 years ago)
            end_date: datetime object (default: now)
            batch_size: number of posts to collect before saving
        """
        
        if start_date is None:
            start_date = datetime.now(timezone.utc) - timedelta(days=730)  # 2 years ago
        if end_date is None:
            end_date = datetime.now(timezone.utc)
            
        logger.info(f"Starting historical scrape from {start_date} to {end_date}")
        logger.info(f"Target user: @{self.username}")
        
        all_posts = []
        batch_count = 0
        total_posts = 0
        
        try:
            # Get user info first
            user_info = self.api.lookup(self.username)
            if not user_info:
                logger.error(f"Could not find user @{self.username}")
                return
                
            logger.info(f"Found user: {user_info.get('display_name', 'N/A')}")
            logger.info(f"Total posts on account: {user_info.get('statuses_count', 'N/A')}")
            
            # Scrape posts in chronological order (oldest first for NLP training)
            logger.info("Starting post collection...")
            
            for post in self.api.pull_statuses(
                username=self.username,
                replies=False,  # Exclude replies for cleaner stance detection data
                verbose=False,
                created_after=start_date
            ):
                # Check if post is within our date range
                post_date = datetime.fromisoformat(post['created_at'].replace('Z', '+00:00'))
                
                if post_date > end_date:
                    continue
                    
                if post_date < start_date:
                    logger.info(f"Reached posts older than start date. Stopping.")
                    break
                
                # Process post for NLP
                processed_post = self.process_post_for_nlp(post)
                all_posts.append(processed_post)
                total_posts += 1
                
                # Save in batches to avoid memory issues
                if len(all_posts) >= batch_size:
                    self.save_batch(all_posts, batch_count)
                    all_posts = []
                    batch_count += 1
                    
                # Progress update
                if total_posts % 50 == 0:
                    logger.info(f"Collected {total_posts} posts so far...")
                    
                # Rate limiting - be respectful
                time.sleep(0.5)
                
        except Exception as e:
            logger.error(f"Error during scraping: {e}")
            
        # Save remaining posts
        if all_posts:
            self.save_batch(all_posts, batch_count)
            
        logger.info(f"Scraping complete! Total posts collected: {total_posts}")
        
        # Combine all batches into final dataset
        self.combine_batches(batch_count + 1)
        
        return total_posts
    
    def process_post_for_nlp(self, post):
        """
        Process a post to extract relevant data for NLP stance detection.
        """
        import re
        
        # Clean HTML from content
        content = re.sub(r'<[^>]+>', '', post.get('content', ''))
        content = re.sub(r'&[a-zA-Z]+;', ' ', content)  # Remove HTML entities
        content = content.strip()
        
        # Extract mentions and hashtags
        mentions = re.findall(r'@(\w+)', content)
        hashtags = re.findall(r'#(\w+)', content)
        
        # Extract URLs
        urls = [attachment.get('url', '') for attachment in post.get('media_attachments', [])]
        
        processed = {
            'id': post['id'],
            'created_at': post['created_at'],
            'content': content,
            'content_length': len(content),
            'mentions': mentions,
            'hashtags': hashtags,
            'media_urls': urls,
            'has_media': len(urls) > 0,
            'engagement': {
                'likes': post.get('favourites_count', 0),
                'reblogs': post.get('reblogs_count', 0),
                'replies': post.get('replies_count', 0)
            },
            'metadata': {
                'language': post.get('language'),
                'sensitive': post.get('sensitive', False),
                'visibility': post.get('visibility', 'public'),
                'url': post.get('url', '')
            },
            # Add fields useful for stance detection
            'nlp_features': {
                'word_count': len(content.split()),
                'sentence_count': len(re.split(r'[.!?]+', content)),
                'exclamation_count': content.count('!'),
                'question_count': content.count('?'),
                'caps_ratio': sum(1 for c in content if c.isupper()) / max(len(content), 1),
                'mention_count': len(mentions),
                'hashtag_count': len(hashtags)
            }
        }
        
        return processed
    
    def save_batch(self, posts, batch_num):
        """Save a batch of posts to file."""
        filename = os.path.join(self.output_dir, f"batch_{batch_num:04d}.json")
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(posts, f, indent=2, ensure_ascii=False)
        logger.info(f"Saved batch {batch_num} with {len(posts)} posts to {filename}")
    
    def combine_batches(self, total_batches):
        """Combine all batch files into a single dataset."""
        all_posts = []
        
        for i in range(total_batches):
            filename = os.path.join(self.output_dir, f"batch_{i:04d}.json")
            if os.path.exists(filename):
                with open(filename, 'r', encoding='utf-8') as f:
                    batch_posts = json.load(f)
                    all_posts.extend(batch_posts)
        
        # Sort by date for chronological analysis
        all_posts.sort(key=lambda x: x['created_at'])
        
        # Save final dataset
        final_filename = os.path.join(self.output_dir, f"trump_historical_dataset_{datetime.now().strftime('%Y%m%d')}.json")
        with open(final_filename, 'w', encoding='utf-8') as f:
            json.dump(all_posts, f, indent=2, ensure_ascii=False)
            
        # Save metadata
        metadata = {
            'total_posts': len(all_posts),
            'date_range': {
                'start': all_posts[0]['created_at'] if all_posts else None,
                'end': all_posts[-1]['created_at'] if all_posts else None
            },
            'collection_date': datetime.now().isoformat(),
            'username': self.username,
            'purpose': 'NLP stance detection training data'
        }
        
        metadata_filename = os.path.join(self.output_dir, f"dataset_metadata_{datetime.now().strftime('%Y%m%d')}.json")
        with open(metadata_filename, 'w', encoding='utf-8') as f:
            json.dump(metadata, f, indent=2, ensure_ascii=False)
            
        logger.info(f"Final dataset saved: {final_filename}")
        logger.info(f"Dataset contains {len(all_posts)} posts")
        
        # Clean up batch files
        for i in range(total_batches):
            filename = os.path.join(self.output_dir, f"batch_{i:04d}.json")
            if os.path.exists(filename):
                os.remove(filename)
        
        return final_filename

def main():
    """Main function to run historical scraping."""
    scraper = HistoricalScraper()
    
    # Define date range (2 years ago to now)
    end_date = datetime.now(timezone.utc)
    start_date = end_date - timedelta(days=730)  # 2 years
    
    print(f"🚀 Starting historical scrape for NLP stance detection")
    print(f"📅 Date range: {start_date.strftime('%Y-%m-%d')} to {end_date.strftime('%Y-%m-%d')}")
    print(f"👤 Target user: @realDonaldTrump")
    print(f"🎯 Purpose: NLP stance detection training data")
    print("-" * 60)
    
    total_posts = scraper.scrape_historical_posts(start_date, end_date)
    
    print("-" * 60)
    print(f"✅ Scraping completed!")
    print(f"📊 Total posts collected: {total_posts}")
    print(f"📁 Data saved in: {scraper.output_dir}/")
    print(f"🔬 Ready for NLP analysis!")

if __name__ == "__main__":
    main()
