#!/usr/bin/env python3
"""
Analyze the scraped Trump Truth Social data.
"""

import json
import re
from datetime import datetime
from collections import Counter

def analyze_posts(filename):
    """Analyze the scraped posts data."""
    
    with open(filename, 'r', encoding='utf-8') as f:
        posts = json.load(f)
    
    print(f"=== ANALYSIS OF {len(posts)} TRUMP POSTS ===\n")
    
    # Basic stats
    total_likes = sum(post.get('favourites_count', 0) for post in posts)
    total_reblogs = sum(post.get('reblogs_count', 0) for post in posts)
    total_replies = sum(post.get('replies_count', 0) for post in posts)
    
    print(f"📊 ENGAGEMENT SUMMARY:")
    print(f"   Total Likes: {total_likes:,}")
    print(f"   Total Reblogs: {total_reblogs:,}")
    print(f"   Total Replies: {total_replies:,}")
    print(f"   Average Likes per post: {total_likes/len(posts):.0f}")
    print(f"   Average Reblogs per post: {total_reblogs/len(posts):.0f}")
    print(f"   Average Replies per post: {total_replies/len(posts):.0f}")
    
    # Most engaged posts
    print(f"\n🔥 TOP 3 MOST LIKED POSTS:")
    sorted_by_likes = sorted(posts, key=lambda x: x.get('favourites_count', 0), reverse=True)
    for i, post in enumerate(sorted_by_likes[:3], 1):
        content = clean_html(post.get('content', ''))[:100]
        print(f"   {i}. {post.get('favourites_count', 0):,} likes - {content}...")
    
    print(f"\n💬 TOP 3 MOST REPLIED POSTS:")
    sorted_by_replies = sorted(posts, key=lambda x: x.get('replies_count', 0), reverse=True)
    for i, post in enumerate(sorted_by_replies[:3], 1):
        content = clean_html(post.get('content', ''))[:100]
        print(f"   {i}. {post.get('replies_count', 0):,} replies - {content}...")
    
    # Time analysis
    print(f"\n⏰ POSTING TIMES:")
    post_times = []
    for post in posts:
        created_at = datetime.fromisoformat(post['created_at'].replace('Z', '+00:00'))
        post_times.append(created_at.hour)
    
    time_counter = Counter(post_times)
    for hour in sorted(time_counter.keys()):
        count = time_counter[hour]
        bar = "█" * (count * 2)  # Simple bar chart
        print(f"   {hour:02d}:00 - {count:2d} posts {bar}")
    
    # Media analysis
    posts_with_media = [p for p in posts if p.get('media_attachments')]
    print(f"\n📸 MEDIA POSTS:")
    print(f"   Posts with media: {len(posts_with_media)}/{len(posts)} ({len(posts_with_media)/len(posts)*100:.1f}%)")
    
    # Content analysis
    print(f"\n📝 CONTENT ANALYSIS:")
    all_text = ' '.join([clean_html(post.get('content', '')) for post in posts])
    words = re.findall(r'\b\w+\b', all_text.lower())
    word_counter = Counter(words)
    
    # Filter out common words
    common_words = {'the', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by', 'a', 'an', 'is', 'are', 'was', 'were', 'be', 'been', 'have', 'has', 'had', 'do', 'does', 'did', 'will', 'would', 'could', 'should', 'may', 'might', 'must', 'can', 'this', 'that', 'these', 'those', 'i', 'you', 'he', 'she', 'it', 'we', 'they', 'me', 'him', 'her', 'us', 'them', 'my', 'your', 'his', 'her', 'its', 'our', 'their'}
    
    filtered_words = {word: count for word, count in word_counter.items() 
                     if len(word) > 2 and word not in common_words}
    
    print(f"   Most common words:")
    for word, count in Counter(filtered_words).most_common(10):
        print(f"     {word}: {count}")
    
    # Recent posts
    print(f"\n🕐 MOST RECENT POSTS:")
    sorted_by_time = sorted(posts, key=lambda x: x['created_at'], reverse=True)
    for i, post in enumerate(sorted_by_time[:3], 1):
        created_at = datetime.fromisoformat(post['created_at'].replace('Z', '+00:00'))
        content = clean_html(post.get('content', ''))[:100]
        print(f"   {i}. {created_at.strftime('%Y-%m-%d %H:%M')} - {content}...")

def clean_html(text):
    """Remove HTML tags from text."""
    import re
    clean = re.compile('<.*?>')
    return re.sub(clean, '', text).strip()

def main():
    """Main function."""
    import glob
    
    # Find the most recent Trump posts file
    json_files = glob.glob('trump_posts_*.json')
    if not json_files:
        print("No Trump posts JSON files found!")
        return
    
    latest_file = max(json_files)
    print(f"Analyzing file: {latest_file}\n")
    
    analyze_posts(latest_file)

if __name__ == "__main__":
    main()
