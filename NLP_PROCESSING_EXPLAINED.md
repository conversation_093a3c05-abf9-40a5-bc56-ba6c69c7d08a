# NLP Data Processing for Stan<PERSON> Detection

## Overview
The data processing pipeline transforms raw Truth Social posts into a structured dataset optimized for NLP stance detection analysis. Here's exactly how each step works:

## Stage 1: Raw Data Collection (`historical_scraper.py`)

### Initial Processing
Each raw post is processed to extract:

```python
def process_post_for_nlp(self, post):
    # Clean HTML from content
    content = re.sub(r'<[^>]+>', '', post.get('content', ''))
    content = re.sub(r'&[a-zA-Z]+;', ' ', content)  # Remove HTML entities
    content = content.strip()
    
    # Extract social media elements
    mentions = re.findall(r'@(\w+)', content)
    hashtags = re.findall(r'#(\w+)', content)
```

### Features Created:
- **Text Content**: HTML-cleaned, ready for analysis
- **Social Elements**: Mentions (@username) and hashtags (#topic)
- **Media**: URLs of attached images/videos
- **Engagement**: Likes, reblogs, replies
- **Basic NLP Features**: Word count, sentence count, punctuation analysis

## Stage 2: Advanced NLP Processing (`prepare_nlp_data.py`)

### Text Cleaning
```python
def clean_text(text):
    # Remove URLs
    text = re.sub(r'http[s]?://(?:[a-zA-Z]|[0-9]|[$-_@.&+]|[!*\\(\\),]|(?:%[0-9a-fA-F][0-9a-fA-F]))+', '', text)
    # Remove extra whitespace
    text = re.sub(r'\s+', ' ', text)
    return text.strip()
```

### Feature Engineering

#### 1. **Linguistic Features**
- `word_count`: Number of words (for text complexity)
- `sentence_count`: Number of sentences (for structure analysis)
- `exclamation_count`: Number of ! (for emotional intensity)
- `question_count`: Number of ? (for rhetorical patterns)
- `caps_ratio`: Proportion of uppercase letters (for emphasis/shouting)

#### 2. **Temporal Features**
- `year`, `month`, `day_of_week`, `hour`: For time-based analysis
- `days_since_start`: Relative timeline position

#### 3. **Topic Features** (Binary)
Automatically identified topics become binary features:
- `topic_america`: True if post mentions "america"
- `topic_bill`: True if post mentions "bill"
- `topic_great`: True if post mentions "great"
- etc.

#### 4. **Engagement Features**
- `engagement_total`: Total interactions (likes + reblogs + replies)
- `like_ratio`: Likes / total engagement
- `reblog_ratio`: Reblogs / total engagement  
- `reply_ratio`: Replies / total engagement
- `engagement_per_word`: Engagement efficiency

#### 5. **Social Features**
- `mention_count`: Number of @mentions
- `hashtag_count`: Number of #hashtags
- `has_media`: Boolean for media presence

## Final Dataset Structure (44 Features)

### Core Text Features
- `content`: Original text
- `cleaned_content`: Processed text for NLP models
- `content_length`: Character count

### Linguistic Analysis Features
- `word_count`, `sentence_count`
- `exclamation_count`, `question_count`
- `caps_ratio`

### Temporal Features
- `created_at`, `year`, `month`, `day_of_week`, `hour`
- `days_since_start`

### Topic Features (15 binary features)
- `topic_bill`, `topic_great`, `topic_america`, etc.

### Engagement Features
- `likes`, `reblogs`, `replies`, `engagement_total`
- `like_ratio`, `reblog_ratio`, `reply_ratio`
- `engagement_per_word`

### Social Features
- `mention_count`, `hashtag_count`, `has_media`
- `mentions`, `hashtags` (comma-separated lists)

## Why This Structure is Perfect for Stance Detection

### 1. **Multi-Modal Analysis**
- **Text**: Primary content for semantic analysis
- **Engagement**: Audience reaction patterns
- **Temporal**: Evolution of stance over time
- **Social**: Network and influence patterns

### 2. **Feature Richness**
- **Linguistic markers**: Emotional intensity, complexity
- **Topic presence**: Specific issue focus
- **Audience response**: Validation of stance effectiveness

### 3. **Ready for ML Models**
- **Numerical features**: Can be used directly in models
- **Binary features**: Perfect for feature selection
- **Normalized ratios**: Scale-independent metrics
- **Clean text**: Ready for transformers, BERT, etc.

## Example Use Cases for Stance Detection

### 1. **Classification Models**
```python
# Features for stance classification
X = df[['word_count', 'caps_ratio', 'exclamation_count', 
        'topic_america', 'topic_great', 'engagement_per_word']]
y = df['stance_label']  # You would add this
```

### 2. **Time Series Analysis**
```python
# Track stance evolution
df_temporal = df.groupby('month').agg({
    'topic_america': 'mean',
    'caps_ratio': 'mean',
    'engagement_total': 'mean'
})
```

### 3. **Text + Features Hybrid**
```python
# Combine BERT embeddings with engineered features
text_features = bert_model(df['cleaned_content'])
numerical_features = df[['caps_ratio', 'engagement_per_word', 'topic_america']]
combined_features = np.concatenate([text_features, numerical_features], axis=1)
```

## Quality Assurance

### Data Filtering
- Posts with <3 words are filtered out (likely not meaningful)
- HTML and URLs are cleaned
- Engagement metrics are normalized

### Feature Validation
- All ratios are bounded [0,1]
- Counts are non-negative integers
- Dates are properly parsed and validated

## Output Formats

### CSV Format
- Easy to load in pandas, R, Excel
- All features as columns
- Ready for scikit-learn, XGBoost, etc.

### JSON Format  
- Preserves data types
- Easy to load in Python
- Compatible with deep learning frameworks

This processing creates a rich, multi-dimensional dataset perfect for sophisticated stance detection analysis!
