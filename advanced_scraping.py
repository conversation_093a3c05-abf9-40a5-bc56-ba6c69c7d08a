#!/usr/bin/env python3

import json
from datetime import datetime, timezone, timedelta
from truthbrush.api import Api

def scrape_posts_since_date(username, days_back=7):
    """Scrape posts from the last N days."""
    api = Api()
    
    # Calculate date threshold
    since_date = datetime.now(timezone.utc) - timedelta(days=days_back)
    
    print(f"Scraping posts from @{username} since {since_date.strftime('%Y-%m-%d')}")
    
    posts = []
    for post in api.pull_statuses(
        username=username,
        created_after=since_date,
        replies=False
    ):
        posts.append(post)
        print(f"Found post from {post['created_at']}")
    
    return posts

def scrape_post_engagement(username, max_posts=5):
    """Scrape posts and their engagement (likes, comments)."""
    api = Api()
    
    posts_with_engagement = []
    
    # Get recent posts
    post_count = 0
    for post in api.pull_statuses(username, replies=False):
        if post_count >= max_posts:
            break
            
        post_id = post['id']
        post_url = f"https://truthsocial.com/@{username}/{post_id}"
        
        print(f"Processing post {post_id}...")
        
        # Get likes
        likes = []
        try:
            for like in api.user_likes(post_url, include_all=True, top_num=100):
                likes.append(like)
        except Exception as e:
            print(f"Error getting likes: {e}")
        
        # Get comments
        comments = []
        try:
            for comment in api.pull_comments(post_url, include_all=True, top_num=50):
                comments.append(comment)
        except Exception as e:
            print(f"Error getting comments: {e}")
        
        # Combine data
        post_data = {
            'post': post,
            'likes': likes,
            'comments': comments,
            'engagement_summary': {
                'likes_count': len(likes),
                'comments_count': len(comments),
                'total_engagement': len(likes) + len(comments)
            }
        }
        
        posts_with_engagement.append(post_data)
        post_count += 1
    
    return posts_with_engagement

def search_trump_mentions():
    """Search for posts mentioning Trump."""
    api = Api()
    
    search_terms = ["Trump", "@realDonaldTrump", "Donald Trump"]
    all_results = []
    
    for term in search_terms:
        print(f"Searching for posts mentioning: {term}")
        
        try:
            for result_batch in api.search(
                searchtype="statuses",  # Search for posts
                query=term,
                limit=10
            ):
                statuses = result_batch.get('statuses', [])
                all_results.extend(statuses)
                
                for status in statuses:
                    print(f"Found mention in post by @{status.get('account', {}).get('acct', 'unknown')}")
                
                break  # Just first batch
                
        except Exception as e:
            print(f"Error searching for '{term}': {e}")
    
    return all_results

def main():
    """Example usage of advanced scraping functions."""
    
    # Example 1: Scrape recent posts
    print("=== Recent Posts ===")
    try:
        recent_posts = scrape_posts_since_date("realDonaldTrump", days_back=3)
        print(f"Found {len(recent_posts)} posts from last 3 days")
    except Exception as e:
        print(f"Error: {e}")
    
    print("\n=== Post Engagement ===")
    try:
        engagement_data = scrape_post_engagement("realDonaldTrump", max_posts=2)
        for i, data in enumerate(engagement_data):
            summary = data['engagement_summary']
            print(f"Post {i+1}: {summary['likes_count']} likes, {summary['comments_count']} comments")
    except Exception as e:
        print(f"Error: {e}")
    
    print("\n=== Trump Mentions ===")
    try:
        mentions = search_trump_mentions()
        print(f"Found {len(mentions)} posts mentioning Trump")
    except Exception as e:
        print(f"Error: {e}")

if __name__ == "__main__":
    main()
