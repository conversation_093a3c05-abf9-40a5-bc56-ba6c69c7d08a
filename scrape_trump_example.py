#!/usr/bin/env python3
"""
Example script to scrape <PERSON>'s Truth Social posts using Truthbrush.

Before running this script:
1. Create a .env file with your Truth Social credentials:
   TRUTHSOCIAL_USERNAME=your_username
   TRUTHSOCIAL_PASSWORD=your_password

2. Make sure you have a Truth Social account and can log in normally.
"""

import json
import sys
from datetime import datetime, timezone
from truthbrush.api import Api

def main():
    # Initialize the API client
    api = Api()
    
    # <PERSON>'s handle on Truth Social
    trump_handle = "realDonaldTrump"
    
    print(f"Scraping posts from @{trump_handle}...")
    
    try:
        # First, let's get <PERSON>'s user information
        print("Getting user information...")
        user_info = api.lookup(trump_handle)
        if user_info:
            print(f"Found user: {user_info.get('display_name', 'N/A')}")
            print(f"Followers: {user_info.get('followers_count', 'N/A')}")
            print(f"Posts: {user_info.get('statuses_count', 'N/A')}")
            print("-" * 50)
        
        # Pull <PERSON>'s recent posts
        print("Fetching recent posts...")
        posts = []
        
        # Get posts (excluding replies by default)
        for post in api.pull_statuses(
            username=trump_handle,
            replies=False,  # Set to True if you want replies too
            verbose=True    # Set to False for less output
        ):
            posts.append(post)
            
            # Print basic info about each post
            print(f"Post ID: {post['id']}")
            print(f"Created: {post['created_at']}")
            print(f"Content: {post['content'][:100]}...")  # First 100 chars
            print(f"Reblogs: {post.get('reblogs_count', 0)}")
            print(f"Favorites: {post.get('favourites_count', 0)}")
            print("-" * 30)
            
            # Stop after 10 posts for this example
            if len(posts) >= 10:
                break
        
        # Save all posts to a JSON file
        output_file = f"trump_posts_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(posts, f, indent=2, ensure_ascii=False)
        
        print(f"\nSaved {len(posts)} posts to {output_file}")
        
    except Exception as e:
        print(f"Error: {e}")
        print("Make sure you have:")
        print("1. Valid Truth Social credentials in .env file")
        print("2. Internet connection")
        print("3. The correct username (try searching first)")
        return 1
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
